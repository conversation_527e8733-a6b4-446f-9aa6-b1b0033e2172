package com.fjdynamics.towermanager.ui.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.width
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.navigation3.rememberViewModelStoreNavEntryDecorator
import androidx.navigation3.runtime.entry
import androidx.navigation3.runtime.entryProvider
import androidx.navigation3.runtime.rememberNavBackStack
import androidx.navigation3.runtime.rememberSavedStateNavEntryDecorator
import androidx.navigation3.ui.NavDisplay
import androidx.navigation3.ui.rememberSceneSetupNavEntryDecorator
import com.fjdynamics.towermanager.R
import com.fjdynamics.towermanager.SettingsNavRoutes
import com.fjdynamics.towermanager.ui.theme.ScreenBackgroundColor
import com.fjdynamics.towermanager.widgets.LocalToaster
import com.fjdynamics.towermanager.widgets.NavigationItem
import com.fjdynamics.towermanager.widgets.TitleBar
import org.koin.androidx.compose.koinViewModel

private val SETTINGS_NAV_ROUTES: List<SettingsNavRoutes> = listOf(
    SettingsNavRoutes.General,
    SettingsNavRoutes.FaceDetection,
    SettingsNavRoutes.Volume,
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onBack: () -> Unit,
    viewModel: SettingsViewModel = koinViewModel()
) {
    val focusManager = LocalFocusManager.current
    val toaster = LocalToaster.current

    LaunchedEffect(Unit) {
        viewModel.saveSuccessEvent.collect {
            focusManager.clearFocus()
            toaster.toast("配置保存成功")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(ScreenBackgroundColor)
    ) {
        TitleBar("系统设置", onBackClick = onBack)

        val settingsBackStack = rememberNavBackStack(SettingsNavRoutes.General)
        Row(modifier = Modifier.fillMaxWidth()) {
            Column(
                modifier = Modifier
                    .width(240.dp)
                    .fillMaxHeight()
                    .background(Color.White)
            ) {
                SETTINGS_NAV_ROUTES.forEach { route ->
                    val isSelected = route == settingsBackStack.lastOrNull()
                    val onNavItemClick = {
                        if (!isSelected) {
                            settingsBackStack.remove(route)
                            settingsBackStack.add(route)
                        }
                    }

                    when (route) {
                        SettingsNavRoutes.General -> {
                            NavigationItem(
                                icon = R.drawable.ic_settings_menu_general,
                                title = "基本属性",
                                onClick = onNavItemClick,
                                selected = isSelected,
                            )
                        }

                        SettingsNavRoutes.FaceDetection -> {
                            NavigationItem(
                                icon = R.drawable.ic_settings_menu_face_detection,
                                title = "人脸识别",
                                onClick = onNavItemClick,
                                selected = isSelected,
                            )
                        }

                        SettingsNavRoutes.Volume -> {
                            NavigationItem(
                                icon = R.drawable.ic_settings_menu_volume,
                                title = "音量控制",
                                onClick = onNavItemClick,
                                selected = isSelected,
                            )
                        }
                    }
                }
            }

            NavDisplay(
                entryDecorators = listOf(
                    rememberSceneSetupNavEntryDecorator(),
                    rememberSavedStateNavEntryDecorator(),
                    rememberViewModelStoreNavEntryDecorator()
                ),
                backStack = settingsBackStack,
                onBack = { },
                entryProvider = entryProvider {
                    entry<SettingsNavRoutes.General> {
                        GeneralSettingsScreen(viewModel)
                    }
                    entry<SettingsNavRoutes.FaceDetection> {
                        FaceDetectionSettingsScreen(viewModel)
                    }
                    entry<SettingsNavRoutes.Volume> {
                        VolumeSettingsScreen(viewModel)
                    }
                },
            )
        }

    }
}
