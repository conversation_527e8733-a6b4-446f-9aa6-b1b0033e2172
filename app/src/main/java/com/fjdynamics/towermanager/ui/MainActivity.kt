package com.fjdynamics.towermanager.ui

import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.addCallback
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.navigation3.rememberViewModelStoreNavEntryDecorator
import androidx.navigation3.runtime.entry
import androidx.navigation3.runtime.entryProvider
import androidx.navigation3.runtime.rememberNavBackStack
import androidx.navigation3.runtime.rememberSavedStateNavEntryDecorator
import androidx.navigation3.ui.NavDisplay
import androidx.navigation3.ui.rememberSceneSetupNavEntryDecorator
import com.fjdynamics.towermanager.MainNavRoutes
import com.fjdynamics.towermanager.ui.home.HomeScreen
import com.fjdynamics.towermanager.ui.login.LoginScreen
import com.fjdynamics.towermanager.ui.settings.SettingsScreen
import com.fjdynamics.towermanager.ui.theme.ScreenBackgroundColor
import com.fjdynamics.towermanager.ui.theme.TowerPadTheme
import com.fjdynamics.towermanager.ui.usercenter.UserCenterScreen
import com.fjdynamics.towermanager.widgets.LocalToaster
import com.fjdynamics.towermanager.widgets.Toaster
import org.koin.androidx.viewmodel.ext.android.viewModel

class MainActivity : ComponentActivity() {
    val viewModel: MainViewModel by viewModel()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        onBackPressedDispatcher.addCallback {}
        enableEdgeToEdge()
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        windowInsetsController.systemBarsBehavior =
            WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        ViewCompat.setOnApplyWindowInsetsListener(window.decorView) { view, windowInsets ->
            windowInsetsController.hide(WindowInsetsCompat.Type.systemBars())
            ViewCompat.onApplyWindowInsets(view, windowInsets)
        }

        val toaster = object : Toaster {
            override fun toast(msg: String, length: Int) {
                runOnUiThread {
                    Toast.makeText(this@MainActivity, msg, length).show()
                }
            }
        }

        setContent {
            TowerPadTheme {
                CompositionLocalProvider(LocalToaster provides toaster) {
                    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
                    if (!uiState.isLoading) {
                        val navBackStack = rememberNavBackStack(uiState.startNavRoute)
                        NavDisplay(
                            entryDecorators = listOf(
                                rememberSceneSetupNavEntryDecorator(),
                                rememberSavedStateNavEntryDecorator(),
                                rememberViewModelStoreNavEntryDecorator()
                            ),
                            modifier = Modifier.Companion
                                .fillMaxSize()
                                .background(ScreenBackgroundColor),
                            backStack = navBackStack,
                            onBack = { },
                            entryProvider = entryProvider {
                                entry(MainNavRoutes.Login) {
                                    LoginScreen(
                                        onNavigateToHome = {
                                            navBackStack.removeLastOrNull()
                                            navBackStack.add(MainNavRoutes.Home)
                                        },
                                        onNavigateToSettings = {
                                            navBackStack.add(MainNavRoutes.Settings)
                                        })
                                }
                                entry(MainNavRoutes.Home) {
                                    HomeScreen(onNavigateToUserCenter = {
                                        navBackStack.add(
                                            MainNavRoutes.UserCenter
                                        )
                                    })
                                }
                                entry(MainNavRoutes.Settings) {
                                    SettingsScreen(onBack = { navBackStack.removeLastOrNull() })
                                }
                                entry(MainNavRoutes.UserCenter) {
                                    UserCenterScreen(
                                        onBack = { navBackStack.removeLastOrNull() },
                                        onLogout = {
                                            navBackStack.clear()
                                            navBackStack.add(MainNavRoutes.Login)
                                        })
                                }
                            }
                        )
                    }
                }
            }
        }
    }
}