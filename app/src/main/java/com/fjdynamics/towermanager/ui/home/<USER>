package com.fjdynamics.towermanager.ui.home

import androidx.annotation.DrawableRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.fjdynamics.towermanager.R
import com.fjdynamics.towermanager.ui.theme.BorderColor
import com.fjdynamics.towermanager.ui.theme.TextPrimaryColor
import com.fjdynamics.towermanager.widgets.TextWithLeadingIcon
import org.koin.androidx.compose.koinViewModel

@Composable
fun HomeScreen(
    onNavigateToUserCenter: () -> Unit,
    viewModel: HomeViewModel = koinViewModel()
) {
    Box(
        modifier = Modifier
            .verticalScroll(rememberScrollState())
            .fillMaxSize()
            .background(Color.White)
    ) {
        val uiState by viewModel.uiState.collectAsStateWithLifecycle()

        Row(
            modifier = Modifier.padding(start = 21.dp, top = 13.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_hook),
                contentDescription = null,
                modifier = Modifier.size(32.dp),
                tint = Color.Unspecified
            )
            Spacer(modifier = Modifier.size(10.dp))
            Text(
                text = "UMTC塔吊中控系统",
                color = TextPrimaryColor,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                fontFamily = FontFamily(Font(R.font.yuan))
            )
        }

        Icon(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = 13.dp, end = 21.dp)
                .size(32.dp)
                .clickable { },
            painter = painterResource(R.drawable.ic_shutdown),
            contentDescription = null,
        )

        HomeInfoCard(
            modifier = Modifier.padding(start = 48.dp, top = 78.dp)
        ) {
            Column {
                TextWithLeadingIcon(R.drawable.ic_project, "项目")
                Spacer(modifier = Modifier.size(12.dp))
                Text(
                    text = uiState.projectInfo?.projectName ?: "",
                    color = TextPrimaryColor,
                    fontSize = 16.sp
                )

                HorizontalDivider(
                    modifier = Modifier.padding(vertical = 15.dp),
                    thickness = 1.dp,
                    color = Color.LightGray
                )

                TextWithLeadingIcon(R.drawable.ic_tower_crane, "塔吊")
                Spacer(modifier = Modifier.size(12.dp))
                Text(
                    text = "${uiState.towerCraneInfo?.name ?: ""} / ${uiState.towerCraneInfo?.model ?: ""}",
                    color = TextPrimaryColor,
                    fontSize = 16.sp
                )
            }
        }

        HomeInfoCard(
            modifier = Modifier
                .padding(start = 48.dp, top = 312.dp)
                .clickable(onClick = onNavigateToUserCenter),
        ) {
            Column(modifier = Modifier.fillMaxSize()) {
                TextWithLeadingIcon(R.drawable.ic_driver, "机手")
                Spacer(modifier = Modifier.size(12.dp))
                Text(
                    text = uiState.userInfo?.name ?: "",
                    color = TextPrimaryColor,
                    fontSize = 16.sp
                )
            }

            Icon(
                painter = painterResource(R.drawable.ic_right_arrow),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .size(27.dp)
            )
        }

        Image(
            painter = painterResource(R.drawable.bg_home_tower_crane),
            contentDescription = null,
            modifier = Modifier.align(Alignment.CenterEnd)
        )

        HomeIconCard(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(bottom = 243.dp, end = 60.dp),
            icon = R.drawable.ic_home_maintenance
        ) { }

        HomeIconCard(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(bottom = 142.dp, end = 60.dp),
            icon = R.drawable.ic_home_task
        ) { }

        HomeIconCard(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(bottom = 41.dp, end = 60.dp),
            icon = R.drawable.ic_home_camera
        ) { }
    }

}

@Composable
fun HomeInfoCard(
    modifier: Modifier = Modifier,
    content: @Composable BoxScope.() -> Unit
) {
    OutlinedCard(
        modifier = modifier.width(333.dp),
        shape = RoundedCornerShape(5.dp),
        colors = CardDefaults.elevatedCardColors(containerColor = Color.White),
        border = BorderStroke(1.dp, BorderColor),
    ) {
        Box(modifier = Modifier.padding(21.dp), content = content)
    }
}

@Composable
fun HomeIconCard(
    @DrawableRes icon: Int,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    OutlinedCard(
        modifier = modifier,
        onClick = onClick,
        colors = CardDefaults.elevatedCardColors(containerColor = Color.White),
        border = BorderStroke(1.dp, BorderColor),
        shape = RoundedCornerShape(5.dp),
    ) {
        Icon(
            modifier = Modifier
                .padding(13.dp)
                .size(58.dp),
            painter = painterResource(icon),
            contentDescription = null,
            tint = Color.Unspecified,
        )
    }
}