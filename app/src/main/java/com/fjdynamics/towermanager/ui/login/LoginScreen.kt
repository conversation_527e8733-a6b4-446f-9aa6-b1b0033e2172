package com.fjdynamics.towermanager.ui.login

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card<PERSON>efaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.fjdynamics.towermanager.R
import com.fjdynamics.towermanager.data.models.UserInfo
import com.fjdynamics.towermanager.ui.theme.BorderColor
import com.fjdynamics.towermanager.ui.theme.ButtonBackgroundColor
import com.fjdynamics.towermanager.ui.theme.ButtonDisabledBackgroundColor
import com.fjdynamics.towermanager.ui.theme.DividerColor
import com.fjdynamics.towermanager.ui.theme.TextHintColor
import com.fjdynamics.towermanager.ui.theme.TextPrimaryColor
import com.fjdynamics.towermanager.widgets.InformDialog
import com.fjdynamics.towermanager.widgets.LoadingDialog
import com.fjdynamics.towermanager.widgets.LocalToaster
import com.fjdynamics.towermanager.widgets.VerifyPasswordDialog
import org.koin.androidx.compose.koinViewModel

@Composable
fun LoginScreen(
    onNavigateToHome: () -> Unit,
    onNavigateToSettings: () -> Unit,
    viewModel: LoginViewModel = koinViewModel(),
) {
    var showForgetPasswordDialog by remember { mutableStateOf(false) }
    var showSettingsEntryDialog by remember { mutableStateOf(false) }
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    val toaster = LocalToaster.current
    LaunchedEffect(Unit) {
        viewModel.sideEffect.collect { sideEffect ->
            when (sideEffect) {
                is LoginSideEffect.ShowToast -> {
                    toaster.toast(sideEffect.msg)
                }

                is LoginSideEffect.NavigateToHome -> {
                    onNavigateToHome()
                }
            }
        }
    }

    Column(
        modifier = Modifier
            .verticalScroll(rememberScrollState())
            .padding(horizontal = 213.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.size(40.dp))

        Row(verticalAlignment = Alignment.CenterVertically) {
            Icon(
                modifier = Modifier.size(48.dp),
                painter = painterResource(id = R.drawable.ic_hook),
                contentDescription = null,
                tint = Color.Unspecified,
            )
            Spacer(modifier = Modifier.width(11.dp))
            Text(
                text = "UMTC塔吊中控系统",
                color = TextPrimaryColor,
                fontSize = 27.sp,
                fontWeight = FontWeight.Bold,
                fontFamily = FontFamily(Font(R.font.yuan)),
            )
        }

        Spacer(modifier = Modifier.size(36.dp))

        LoginInputCard(
            historyUserList = uiState.historyUsers,
            modifier = Modifier.fillMaxWidth(),
            username = uiState.inputUsername,
            password = uiState.inputPassword,
            onUsernameChange = { viewModel.onUsernameChanged(it) },
            onPasswordChange = { viewModel.onPasswordChanged(it) },
            onHistoryUserSelect = { userInfo ->
                viewModel.onUsernameChanged(userInfo.username)
                viewModel.onPasswordChanged(userInfo.password)
            },
            onHistoryUserDelete = {
                viewModel.deleteUser(it)
            }
        )

        Spacer(modifier = Modifier.size(21.dp))

        CircularCheckbox(
            modifier = Modifier.align(Alignment.Start),
            checked = uiState.rememberPassword,
            text = "记住密码"
        ) { checked ->
            viewModel.setRememberPassword(checked)
        }

        Spacer(modifier = Modifier.size(32.dp))

        Button(
            onClick = { viewModel.handleUserLogin() },
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(5.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = ButtonBackgroundColor,
                disabledContainerColor = ButtonDisabledBackgroundColor
            ),
            enabled = uiState.inputUsername.isNotEmpty() && uiState.inputPassword.isNotEmpty(),
            contentPadding = PaddingValues(vertical = 16.dp),
        ) {
            Text(text = "登录", color = Color.White, fontSize = 21.sp, fontWeight = FontWeight.Bold)
        }

        Spacer(modifier = Modifier.size(42.dp))

        Row {
            Text(
                text = "忘记密码",
                color = TextPrimaryColor,
                fontSize = 21.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.clickable { showForgetPasswordDialog = true }
            )
            Spacer(modifier = Modifier.size(61.dp))
            Text(
                text = "系统设置",
                color = TextPrimaryColor,
                fontSize = 21.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.clickable { showSettingsEntryDialog = true }
            )
        }

        if (showForgetPasswordDialog) {
            InformDialog(
                message = "请联系系统管理员重置密码！",
                onDismissRequest = { showForgetPasswordDialog = false },
                onConfirmation = { showForgetPasswordDialog = false },
            )
        }

        if (showSettingsEntryDialog) {
            VerifyPasswordDialog(
                password = "999999",
                onDismissRequest = { showSettingsEntryDialog = false },
                onPasswordMatched = {
                    showSettingsEntryDialog = false
                    onNavigateToSettings()
                },
            )
        }

        if (uiState.isLoading) {
            LoadingDialog(uiState.loadingMsg)
        }
    }
}

@Composable
fun LoginInputCard(
    historyUserList: List<UserInfo>,
    username: String,
    password: String,
    onUsernameChange: (String) -> Unit,
    onPasswordChange: (String) -> Unit,
    onHistoryUserSelect: (UserInfo) -> Unit,
    onHistoryUserDelete: (UserInfo) -> Unit,
    modifier: Modifier = Modifier,
) {
    var passwordVisible by remember { mutableStateOf(false) }
    var textFieldWidth by remember { mutableStateOf(Dp.Unspecified) }
    val density = LocalDensity.current
    var showHistoryUsersPopup by remember { mutableStateOf(false) }

    OutlinedCard(
        modifier = modifier,
        shape = RoundedCornerShape(5.dp),
        colors = CardDefaults.outlinedCardColors(containerColor = Color.White),
        border = BorderStroke(1.dp, BorderColor)
    ) {
        Column(
            modifier = Modifier.padding(horizontal = 21.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Box {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.onGloballyPositioned { coordinates ->
                        textFieldWidth = with(density) { coordinates.size.width.toDp() }
                    }) {
                    Icon(
                        modifier = Modifier.size(32.dp),
                        painter = painterResource(id = R.drawable.ic_user),
                        contentDescription = null,
                    )
                    BasicTextField(
                        modifier = Modifier
                            .weight(1f)
                            .padding(12.dp),
                        value = username,
                        onValueChange = onUsernameChange,
                        singleLine = true,
                        textStyle = TextStyle(
                            color = TextPrimaryColor,
                            fontSize = 21.sp,
                        ),
                        decorationBox = { innerTextField ->
                            Box(modifier = Modifier.padding(vertical = 12.dp)) {
                                if (username.isEmpty()) {
                                    Text(
                                        text = "帐号",
                                        color = TextHintColor,
                                        style = TextStyle(fontSize = 21.sp, color = TextHintColor)
                                    )
                                }
                                innerTextField()
                            }
                        },
                    )
                    Icon(
                        modifier = Modifier
                            .size(32.dp)
                            .clickable {
                                showHistoryUsersPopup = true
                            }
                            .clip(CircleShape),
                        painter = painterResource(id = R.drawable.ic_login_drop_down),
                        contentDescription = null
                    )
                }

                DropdownMenu(
                    modifier = Modifier.width(textFieldWidth),
                    expanded = showHistoryUsersPopup && historyUserList.isNotEmpty(),
                    onDismissRequest = { showHistoryUsersPopup = false },
                    containerColor = Color.White,
                ) {
                    historyUserList.forEachIndexed { index, userInfo ->
                        DropdownMenuItem(
                            text = {
                                Text(
                                    text = userInfo.username,
                                    color = TextPrimaryColor,
                                    fontSize = 18.sp
                                )
                            },
                            onClick = {
                                showHistoryUsersPopup = false
                                onHistoryUserSelect(userInfo)
                            },
                            contentPadding = PaddingValues(horizontal = 21.dp),
                            trailingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Clear,
                                    contentDescription = null,
                                    modifier = Modifier.clickable { onHistoryUserDelete(userInfo) }
                                )
                            }
                        )

                        if (index != historyUserList.lastIndex) {
                            HorizontalDivider(
                                modifier = Modifier.padding(horizontal = 21.dp),
                                thickness = 1.dp,
                                color = DividerColor
                            )
                        }
                    }
                }

            }

            HorizontalDivider(thickness = 1.dp, color = DividerColor)

            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    modifier = Modifier.size(32.dp),
                    painter = painterResource(id = R.drawable.ic_password),
                    contentDescription = null,
                )

                BasicTextField(
                    modifier = Modifier
                        .weight(1f)
                        .padding(12.dp),
                    value = password,
                    onValueChange = onPasswordChange,
                    singleLine = true,
                    textStyle = TextStyle(
                        color = TextPrimaryColor,
                        fontSize = 21.sp,
                    ),
                    visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    decorationBox = { innerTextField ->
                        Box(modifier = Modifier.padding(vertical = 12.dp)) {
                            if (password.isEmpty()) {
                                Text(
                                    text = "密码",
                                    color = TextHintColor,
                                    style = TextStyle(fontSize = 21.sp, color = TextHintColor)
                                )
                            }
                            innerTextField()
                        }
                    },
                )

                Icon(
                    modifier = Modifier
                        .size(32.dp)
                        .clickable { passwordVisible = !passwordVisible },
                    painter = painterResource(id = if (passwordVisible) R.drawable.ic_show_password else R.drawable.ic_hide_password),
                    contentDescription = null
                )
            }
        }
    }
}

@Composable
fun CircularCheckbox(
    modifier: Modifier = Modifier,
    checked: Boolean,
    text: String,
    onCheckChanged: (Boolean) -> Unit
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .clickable { onCheckChanged(!checked) }
            .padding(vertical = 4.dp)
    ) {
        Icon(
            modifier = Modifier.size(30.dp),
            painter = painterResource(id = if (checked) R.drawable.ic_remember_checked else R.drawable.ic_remember_unchecked),
            contentDescription = null,
            tint = if (checked) Color(0xFF0091FA) else LocalContentColor.current
        )
        Spacer(modifier = Modifier.size(12.dp))
        Text(
            text = text,
            color = TextPrimaryColor,
            fontSize = 21.sp,
        )
    }
}