package com.fjdynamics.towermanager.widgets

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.fjdynamics.towermanager.R
import com.fjdynamics.towermanager.ui.theme.TextPrimaryColor
import com.fjdynamics.towermanager.utils.DebounceClick

@Composable
fun TitleBar(
    title: String,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(58.dp)
            .background(Color.White)
    ) {
        Icon(
            modifier = Modifier
                .padding(start = 21.dp)
                .size(24.dp)
                .align(Alignment.CenterStart)
                .clickable { DebounceClick.processEvent(onBackClick) },
            painter = painterResource(R.drawable.ic_black_back),
            contentDescription = null
        )

        Text(
            modifier = Modifier.align(Alignment.Center),
            text = title,
            color = TextPrimaryColor,
            fontSize = 24.sp
        )
    }
}