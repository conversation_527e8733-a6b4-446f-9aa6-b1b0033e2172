package com.fjdynamics.towermanager.di

import com.fjdynamics.towermanager.BuildConfig
import com.fjdynamics.towermanager.data.network.AuthProvider
import com.fjdynamics.towermanager.data.network.PLATFORM_API_HOST
import com.fjdynamics.towermanager.data.network.PlatformApi
import com.fjdynamics.towermanager.data.network.PlatformApiImpl
import com.fjdynamics.towermanager.data.persistent.userDatastore
import com.fjdynamics.towermanager.utils.logger
import io.ktor.client.HttpClient
import io.ktor.client.engine.okhttp.OkHttp
import io.ktor.client.plugins.HttpSend
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logger
import io.ktor.client.plugins.logging.Logging
import io.ktor.client.plugins.logging.LoggingFormat
import io.ktor.client.plugins.plugin
import io.ktor.client.plugins.websocket.WebSockets
import io.ktor.serialization.kotlinx.json.json
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.serialization.json.Json
import okhttp3.OkHttpClient
import org.koin.android.ext.koin.androidContext
import org.koin.core.scope.Scope
import org.koin.dsl.module
import java.util.concurrent.TimeUnit

val Scope.httpClient get() = get<HttpClient>()

fun networkModule() = module {
    single<HttpClient> {
        HttpClient(OkHttp) {
            engine {
                preconfigured = OkHttpClient.Builder()
                    .pingInterval(15, TimeUnit.SECONDS)
                    .build()
            }
            install(WebSockets)
            install(Logging) {
                logger = object : Logger {
                    override fun log(message: String) {
                        logger("ktor").debug(message)
                    }
                }
                level = if (BuildConfig.DEBUG) LogLevel.ALL else LogLevel.INFO
                format = LoggingFormat.OkHttp
            }
            install(ContentNegotiation) {
                json(get<Json>())
            }
            install(HttpTimeout) {
                connectTimeoutMillis = 30_000L
                socketTimeoutMillis = 30_000L
                requestTimeoutMillis = 30_000L
            }
        }.also { httpClient ->
            httpClient.plugin(HttpSend).intercept { request ->
                val requestUrl = request.url.toString()
                if (requestUrl.startsWith(PLATFORM_API_HOST)) {
                    get<AuthProvider>().authFlow().firstOrNull()?.let { authInfo ->
                        request.headers.append("Authorization", authInfo.token)
                        request.headers.append("x-platform-header", authInfo.platformId)
                        request.headers.append("x-organization-header", authInfo.orgId)
                    }
                }

                execute(request)
            }
        }
    }

    single<PlatformApi> {
        PlatformApiImpl(httpClient)
    }

    single<AuthProvider> {
        AuthProvider(androidContext().userDatastore, get<Json>())
    }
}