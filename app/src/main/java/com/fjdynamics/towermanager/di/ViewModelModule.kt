package com.fjdynamics.towermanager.di

import com.fjdynamics.towermanager.ui.MainViewModel
import com.fjdynamics.towermanager.ui.home.HomeViewModel
import com.fjdynamics.towermanager.ui.login.LoginViewModel
import com.fjdynamics.towermanager.ui.settings.SettingsViewModel
import com.fjdynamics.towermanager.ui.usercenter.UserCenterViewModel
import org.koin.core.module.dsl.viewModelOf
import org.koin.dsl.module

fun viewModelModule() = module {
    viewModelOf(::MainViewModel)
    viewModelOf(::LoginViewModel)
    viewModelOf(::SettingsViewModel)
    viewModelOf(::HomeViewModel)
    viewModelOf(::UserCenterViewModel)
}