package com.fjdynamics.towermanager.data.network

import com.fjdynamics.towermanager.TowerCranePadApp
import com.fjdynamics.towermanager.utils.thisLogger
import io.ktor.client.HttpClient
import io.ktor.client.plugins.websocket.DefaultClientWebSocketSession
import io.ktor.client.plugins.websocket.webSocket
import io.ktor.websocket.Frame
import io.ktor.websocket.close
import io.ktor.websocket.readText
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicBoolean


abstract class WebSocketClient(
    val host: String,
    val port: Int,
    val path: String,
    val httpClient: HttpClient,
) {
    private val logger = thisLogger()
    private var webSocketSession: DefaultClientWebSocketSession? = null
    private var running = AtomicBoolean(false)

    suspend fun connect(onMessage: suspend (String) -> Unit) =
        withContext(Dispatchers.IO + SupervisorJob()) {
            if (!running.compareAndSet(false, true)) {
                logger.warn("connect failed, already running")
                return@withContext
            }
            while (running.get()) {
                try {
                    logger.debug("start connect to $host:$port$path")
                    httpClient.webSocket(host = host, port = port, path = path) {
                        logger.info("connected to $host:$port$path")
                        webSocketSession = this
                        while (true) {
                            val msg = incoming.receive() as? Frame.Text ?: continue
                            onMessage.invoke(msg.readText())
                        }
                    }
                } catch (e: Exception) {
                    logger.error("connect exception", e)
                }
                webSocketSession = null
                delay(5_000)
            }
        }

    suspend fun disconnect() {
        webSocketSession?.close()
        running.set(false)
    }
}


class CentralWsClient(
    httpClient: HttpClient,
) : WebSocketClient(
    "*************",
    8070,
    "/monitor/mini_ctrl/${if (TowerCranePadApp.IS_PROD) "prod" else "test"}",
    httpClient
)

class ControlWsClient(
    httpClient: HttpClient,
) : WebSocketClient(
    "************",
    10086,
    "/",
    httpClient
)