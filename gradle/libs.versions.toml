[versions]
agp = "8.11.1"
datastore = "1.1.7"
kotlin = "2.2.0"
ksp = "2.2.0-2.0.2"
coreKtx = "1.16.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
lifecycleRuntimeKtx = "2.9.2"
activityCompose = "1.10.1"
viewModelCompose = "2.9.2"
composeBom = "2025.07.00"
room = "2.7.2"
navigation3 = "1.0.0-alpha05"
navigation3Lifecycle = "1.0.0-alpha03"
koin = "4.1.0"
ktor = "3.2.2"
slf4j = "2.0.17"
logbackAndroid = "3.0.0"

[libraries]
#AndroidX
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-datastore-preferences = { module = "androidx.datastore:datastore-preferences", version.ref = "datastore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-viewmodel-compose = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "viewModelCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-comsose-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-compose-material3 = { group = "androidx.compose.material3", name = "material3" }
#Test
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
#Navigation3
androix-navigation3-runtime = { group = "androidx.navigation3", name = "navigation3-runtime", version.ref = "navigation3" }
androix-navigation3-ui = { group = "androidx.navigation3", name = "navigation3-ui", version.ref = "navigation3" }
androidx-naviation3-lifecycle = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-navigation3-android", version.ref = "navigation3Lifecycle" }
#Room
androidx-room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
androidx-room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
#Serialization
kotlinx-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version = "1.9.0" }
#Koin
koin-bom = { module = "io.insert-koin:koin-bom", version.ref = "koin" }
koin-android = { module = "io.insert-koin:koin-android" }
koin-compose = { module = "io.insert-koin:koin-androidx-compose" }
#AutoSize
autosize = { group = "com.github.JessYanCoding", name = "AndroidAutoSize", version = "1.2.1" }
#Ktor
ktor-client-core = { module = "io.ktor:ktor-client-core", version.ref = "ktor" }
ktor-client-okhttp = { module = "io.ktor:ktor-client-okhttp", version.ref = "ktor" }
ktor-client-content-negotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktor" }
ktor-client-logging = { module = "io.ktor:ktor-client-logging", version.ref = "ktor" }
ktor-client-websocket = { module = "io.ktor:ktor-client-websocket", version.ref = "ktor" }
ktor-serialization-kotlinx-json = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "ktor" }
#Logging
slf4j-api = { group = "org.slf4j", name = "slf4j-api", version.ref = "slf4j" }
logback-android = { group = "com.github.tony19", name = "logback-android", version.ref = "logbackAndroid" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
androidx-room = { id = "androidx.room", version.ref = "room" }

[bundles]
compose = [
    "androidx-comsose-ui",
    "androidx-compose-material3",
    "androidx-activity-compose",
    "androidx-viewmodel-compose"
]
room = [
    "androidx-room-runtime",
    "androidx-room-ktx"
]
navigation = [
    "androix-navigation3-runtime",
    "androix-navigation3-ui",
    "androidx-naviation3-lifecycle",
]
koin = [
    "koin-android",
    "koin-compose"
]
ktor-client = [
    "ktor-client-core",
    "ktor-client-okhttp",
    "ktor-client-content-negotiation",
    "ktor-client-logging",
    "ktor-client-websocket",
    "ktor-serialization-kotlinx-json",
]
logging = [
    "slf4j-api",
    "logback-android"
]

